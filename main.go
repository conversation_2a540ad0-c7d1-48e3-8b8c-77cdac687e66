package main

import (
	"crypto/tls"
	"fmt"
	"log"
	"net/smtp"
	"os"
	"strings"
	"time"
)

// EmailConfig 郵件配置結構
type EmailConfig struct {
	SMTPHost  string
	SMTPPort  string
	Username  string
	Password  string
	FromEmail string
	FromName  string
}

// EmailMessage 郵件訊息結構
type EmailMessage struct {
	To      []string
	Subject string
	Body    string
}

// SendEmail 發送郵件函數
func SendEmail(config EmailConfig, message EmailMessage) error {
	// 建立 SMTP 認證
	auth := smtp.PlainAuth("", config.Username, config.Password, config.SMTPHost)

	// 建立 TLS 配置
	tlsConfig := &tls.Config{
		InsecureSkipVerify: false,
		ServerName:         config.SMTPHost,
	}

	// 連接到 SMTP 伺服器
	conn, err := tls.Dial("tcp", config.SMTPHost+":"+config.SMTPPort, tlsConfig)
	if err != nil {
		return fmt.Errorf("連接 SMTP 伺服器失敗: %v", err)
	}
	defer conn.Close()

	// 建立 SMTP 客戶端
	client, err := smtp.NewClient(conn, config.SMTPHost)
	if err != nil {
		return fmt.Errorf("建立 SMTP 客戶端失敗: %v", err)
	}
	defer client.Quit()

	// 進行認證
	if err = client.Auth(auth); err != nil {
		return fmt.Errorf("SMTP 認證失敗: %v", err)
	}

	// 設定發件人
	if err = client.Mail(config.FromEmail); err != nil {
		return fmt.Errorf("設定發件人失敗: %v", err)
	}

	// 設定收件人
	for _, to := range message.To {
		if err = client.Rcpt(to); err != nil {
			return fmt.Errorf("設定收件人 %s 失敗: %v", to, err)
		}
	}

	// 準備郵件內容
	writer, err := client.Data()
	if err != nil {
		return fmt.Errorf("準備郵件內容失敗: %v", err)
	}

	// 構建郵件頭部和內容
	emailContent := fmt.Sprintf(
		"From: %s <%s>\r\n"+
			"To: %s\r\n"+
			"Subject: %s\r\n"+
			"MIME-Version: 1.0\r\n"+
			"Content-Type: text/plain; charset=UTF-8\r\n"+
			"\r\n"+
			"%s\r\n",
		config.FromName,
		config.FromEmail,
		strings.Join(message.To, ", "),
		message.Subject,
		message.Body,
	)

	// 寫入郵件內容
	_, err = writer.Write([]byte(emailContent))
	if err != nil {
		return fmt.Errorf("寫入郵件內容失敗: %v", err)
	}

	// 關閉寫入器
	err = writer.Close()
	if err != nil {
		return fmt.Errorf("關閉郵件寫入器失敗: %v", err)
	}

	return nil
}

// LogError 記錄錯誤到檔案
func LogError(err error) {
	logFile, fileErr := os.OpenFile("email_error.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if fileErr != nil {
		fmt.Printf("無法開啟日誌檔案: %v\n", fileErr)
		return
	}
	defer logFile.Close()

	logger := log.New(logFile, "", log.LstdFlags)
	logger.Printf("郵件發送錯誤: %v\n", err)
}

func main() {
	fmt.Println("=== 郵件發送測試程式 ===")
	fmt.Println("時間:", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Println()

	// 郵件配置
	config := EmailConfig{
		SMTPHost:  "cuisinart.com.tw",
		SMTPPort:  "465",
		Username:  "<EMAIL>",
		Password:  "cui$()n9e#!", // 請在這裡填入您的密碼
		FromEmail: "<EMAIL>",
		FromName:  "Cuisinart Admin",
	}

	// 檢查密碼是否已設定
	if config.Password == "" {
		fmt.Println("❌ 錯誤: 請先在程式碼中設定密碼")
		fmt.Println("請編輯 main.go 檔案，在 Password 欄位填入您的密碼")
		return
	}

	// 測試郵件內容
	message := EmailMessage{
		To:      []string{"<EMAIL>"}, // 發送給自己測試
		Subject: "郵件伺服器測試 - " + time.Now().Format("2006-01-02 15:04:05"),
		Body: `這是一封測試郵件。

發送時間: ` + time.Now().Format("2006-01-02 15:04:05") + `
伺服器: cuisinart.com.tw:465
協定: SMTP over SSL/TLS

如果您收到這封郵件，表示郵件伺服器配置正確。

測試完成。`,
	}

	fmt.Printf("正在發送測試郵件...\n")
	fmt.Printf("SMTP 伺服器: %s:%s\n", config.SMTPHost, config.SMTPPort)
	fmt.Printf("發件人: %s\n", config.FromEmail)
	fmt.Printf("收件人: %s\n", strings.Join(message.To, ", "))
	fmt.Printf("主題: %s\n", message.Subject)
	fmt.Println()

	// 發送郵件
	err := SendEmail(config, message)
	if err != nil {
		fmt.Printf("❌ 郵件發送失敗: %v\n", err)
		LogError(err)
		fmt.Println("錯誤已記錄到 email_error.log 檔案")
	} else {
		fmt.Println("✅ 郵件發送成功！")
		fmt.Println("請檢查收件匣確認是否收到測試郵件")
	}

	fmt.Println()
	fmt.Println("程式執行完成")
}
