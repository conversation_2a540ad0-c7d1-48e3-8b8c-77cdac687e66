# 郵件發送測試程式

這是一個用 Go 語言編寫的郵件發送測試程式，專門用於測試 cuisinart.com.tw 郵件伺服器。

## 郵件伺服器配置

- **SMTP 伺服器**: cuisinart.com.tw
- **端口**: 465 (SSL/TLS)
- **用戶名**: <EMAIL>
- **協定**: SMTP over SSL/TLS

## 使用方法

1. **設定密碼**：
   編輯 `main.go` 檔案，找到以下行：
   ```go
   Password:  "", // 請在這裡填入您的密碼
   ```
   將您的郵件密碼填入雙引號中。

2. **執行程式**：
   ```bash
   go run main.go
   ```

3. **檢查結果**：
   - 如果成功，程式會顯示 "✅ 郵件發送成功！"
   - 如果失敗，錯誤訊息會顯示在終端機，並記錄到 `email_error.log` 檔案

## 功能特點

- ✅ 使用 SSL/TLS 加密連接 (端口 465)
- ✅ 支援 SMTP 認證
- ✅ 詳細的錯誤記錄
- ✅ 自動生成測試郵件內容
- ✅ 錯誤日誌記錄到檔案

## 故障排除

### 常見錯誤

1. **認證失敗**：
   - 檢查用戶名和密碼是否正確
   - 確認郵件帳號是否啟用

2. **連接失敗**：
   - 檢查網路連接
   - 確認防火牆設定
   - 驗證 SMTP 伺服器地址和端口

3. **TLS/SSL 錯誤**：
   - 確認伺服器支援 SSL/TLS
   - 檢查憑證是否有效

### 日誌檔案

所有錯誤都會記錄到 `email_error.log` 檔案中，包含時間戳記和詳細錯誤訊息。

## 程式結構

- `EmailConfig`: 郵件伺服器配置結構
- `EmailMessage`: 郵件訊息結構  
- `SendEmail()`: 主要的郵件發送函數
- `LogError()`: 錯誤記錄函數

## 安全注意事項

- 不要將密碼提交到版本控制系統
- 建議使用環境變數或配置檔案來存儲敏感資訊
- 測試完成後請移除程式碼中的密碼
